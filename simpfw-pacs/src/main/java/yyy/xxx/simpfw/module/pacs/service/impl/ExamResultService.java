package yyy.xxx.simpfw.module.pacs.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import yyy.xxx.simpfw.common.annotation.DataSource;
import yyy.xxx.simpfw.common.core.domain.HttpResult;
import yyy.xxx.simpfw.common.core.service.HttpClientService;
import yyy.xxx.simpfw.common.enums.DataSourceType;
import yyy.xxx.simpfw.common.utils.StringUtils;
import yyy.xxx.simpfw.module.pacs.component.JsonConfigService;
import yyy.xxx.simpfw.module.pacs.constants.OCRTaskStatus;
import yyy.xxx.simpfw.module.pacs.constants.TableMetadata;
import yyy.xxx.simpfw.module.pacs.dto.ExamFileEntityExample;
import yyy.xxx.simpfw.module.pacs.dto.RefEntityExample;
import yyy.xxx.simpfw.module.pacs.entity.ExamFileEntity;
import yyy.xxx.simpfw.module.pacs.entity.ExamInfo;
import yyy.xxx.simpfw.module.pacs.entity.ExamResultEntityWithBLOBs;
import yyy.xxx.simpfw.module.pacs.entity.RefEntity;
import yyy.xxx.simpfw.module.pacs.mapper.ExamResultEntityMapper;
import yyy.xxx.simpfw.module.pacs.ocr.*;
import yyy.xxx.simpfw.module.pacs.service.ExamInfoService;
import yyy.xxx.simpfw.module.pacs.utils.FileUtil;
import yyy.xxx.simpfw.module.pacs.vo.ExamResultVo;
import yyy.xxx.simpfw.system.service.ISysConfigService;

import java.net.URL;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Slf4j
@DataSource(value = DataSourceType.SLAVE)
public class ExamResultService {

    private final ExamResultEntityMapper examResultEntityMapper;

    private final HttpClientService httpClientService;

    private final ISysConfigService configService;

    private final JsonConfigService jsonConfigService;

    private final RefService refService;

    private final ExamInfoService examInfoService;

    private final ExamFileService examFileService;

    private final OCRService ocrService;


    public ExamResultService(ExamResultEntityMapper examResultEntityMapper,
                             HttpClientService httpClientService,
                             ISysConfigService configService,
                             JsonConfigService jsonConfigService,
                             RefService refService,
                             ExamInfoService examInfoService,
                             ExamFileService examFileService,
                             OCRService ocrService) {
        this.examResultEntityMapper = examResultEntityMapper;
        this.httpClientService = httpClientService;
        this.configService = configService;
        this.jsonConfigService = jsonConfigService;
        this.refService = refService;
        this.examInfoService = examInfoService;
        this.examFileService = examFileService;
        this.ocrService = ocrService;
    }

    public ExamResultVo computeResultData(ExamResultVo examResultVo) throws Exception {
        String reportConfigJS = getSysConfig("reportDesignTemplateConfig");
        JSONObject reportConfig = JSON.parseObject(reportConfigJS);
        String baseUrl = reportConfig.getString("baseUrl");
        URL urlParser = new URL(baseUrl);
        baseUrl = urlParser.getProtocol() + "://" + urlParser.getHost() + (urlParser.getPort() != -1 ? ":" + urlParser.getPort() : "");
        String reportResultUrl = baseUrl + reportConfig.getString("reportResultApi");

        String reportResultTemplate = jsonConfigService.getReportResultTemplate(examResultVo.getExamItemCode());
        if (StringUtils.isBlank(reportResultTemplate)) {
            throw new RuntimeException("检查项目: " + examResultVo.getExamItemCode() + " 下的reportResultTemplate为空");
        }
        examResultVo.setTemplatePath(reportResultTemplate);

        log.info("开始请求：{}", reportResultUrl);
        Map<String, Object> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        HttpResult httpResult = httpClientService.doPost(reportResultUrl, JSON.toJSONString(examResultVo), headers, null);
        log.info("报表计算结构化数据返回：{}", httpResult);
        ExamResultVo computeResult = JSON.parseObject(httpResult.getBody(), ExamResultVo.class);
        examResultVo.setExamConclusion(computeResult.getExamConclusion());
        examResultVo.setExamDiagnosis(computeResult.getExamDiagnosis());
        examResultVo.setExamSuggestion(computeResult.getExamSuggestion());
        return examResultVo;
    }

    public void insert(ExamResultVo examResultVo) {
        ExamResultEntityWithBLOBs entity = examResultVo.toEntity();
        insert(entity);
        examResultVo.setId(String.valueOf(entity.getId()));
        examResultVo.setReportId(entity.getReportId());
    }

    @DataSource(value = DataSourceType.SLAVE)
    public void insert(ExamResultEntityWithBLOBs entity) {
        String reportId = examInfoService.makeExamNo(null, ".examResult");
        entity.setReportId(reportId);
        examResultEntityMapper.insertSelective(entity);
    }

    public void insertRef(String examSerialNo,
                          String reportId) {
        Long examSerilNoLong = Long.valueOf(examSerialNo);
        Long reportIdLong = Long.valueOf(reportId);
        RefEntityExample example = new RefEntityExample();
        example.createCriteria().andObjectId1EqualTo(examSerilNoLong);
        List<RefEntity> currentRefs = refService.getRefList(TableMetadata.R_EXAM_INFO_EXAM_RESULT, example);
        if (CollectionUtils.isNotEmpty(currentRefs)) {
            log.info("检查 {} 已存在examResult引用 {}, 不进行自动匹配", examSerialNo, currentRefs);
            return;
        }
        RefEntity refEntity = new RefEntity();
        refEntity.setRefTableName(TableMetadata.R_EXAM_INFO_EXAM_RESULT);
        refEntity.setObjectId1(examSerilNoLong);
        refEntity.setObjectId2(reportIdLong);
        refService.fullUpdateByObjectId1(Collections.singletonList(refEntity));
    }

    // 检查结果结构化信息立即or定时入库
    public void executeExamResultTask(ExamFileEntity examFileEntity) {
        List<ExamFileEntity> fileEntities;
        if (examFileEntity != null && examFileEntity.getOcrDiagTaskStatus() == OCRTaskStatus.PENDING) {
            log.info("开始执行任务：检查结果结构化信息立即入库");
            fileEntities = Collections.singletonList(examFileEntity);
        } else {
            log.info("开始执行任务：检查结果结构化信息定时入库");
            ExamFileEntityExample example = new ExamFileEntityExample();
            ExamFileEntityExample.Criteria criteria = example.createCriteria();
            criteria.andOCRDiagTaskStatusEqualTo(OCRTaskStatus.PENDING.getCode());
            criteria.andExamUidIsNotNull();
            fileEntities = examFileService.selectByExample(example);
        }

        if (fileEntities.isEmpty()) {
            log.info("待提取检查结果的文件列表为空，结束任务");
            return;
        }
        String serverUrl = ocrService.getOCRServerUrl();
        Map<String, List<FunctionDefinition>> AllFunctionMap = ocrService.getAllFunctionMapping();

        for (ExamFileEntity taskEntity : fileEntities) {
            CompletableFuture.runAsync(() -> {
                taskEntity.setOcrDiagTaskStatus(OCRTaskStatus.RUNNING);
                examFileService.updateByPrimaryKeySelective(taskEntity);
                List<FunctionDefinition> functionDefinitions = AllFunctionMap.get(taskEntity.getOcrFileLabel());
                List<Object> examResultEntities = new ArrayList<>();

                for (FunctionDefinition functionDefinition : functionDefinitions) {
                    if (functionDefinition.getFunctionType() == FunctionType.DIAG_AREA) {
                        FunctionParam functionParam = functionDefinition.getFunctionParam();
                        if (functionParam == null) {
                            throw new IllegalArgumentException("请在DIAG_AREA方法中配置方法参数functionParam");
                        }
                        MatchArea matchArea = functionParam.getMatchArea();
                        if (matchArea == null) {
                            throw new IllegalArgumentException("请在DIAG_AREA方法中配置识别区域matchArea");
                        }
                        List<FieldInfo> saveValues = functionParam.getSaveValues();
                        if (saveValues == null) {
                            throw new IllegalArgumentException("请在DIAG_AREA方法中配置对应的业务字段appendValues");
                        }
                        String filePath = taskEntity.getFilePath();
                        byte[] file = FileUtil.readFileAsByteArray(filePath);
                        String ocrResult = ocrService.pdfOCR(serverUrl, file, matchArea, false)
                                .stream().map(OCRTextInfo::getContent).collect(Collectors.joining());

                        FieldSetter.clear(); //清空上一个OCR结果缓存的实例
                        FunctionType.handleFieldSetter(functionParam, ocrResult, examResultEntities);
                        for (Object examResultObj : examResultEntities) {
                            if (examResultObj instanceof ExamResultEntityWithBLOBs) {
                                ExamResultEntityWithBLOBs examResultEntity = (ExamResultEntityWithBLOBs) examResultObj;
                                examResultEntity.setExamItemCode(examFileEntity.getExamItemCode());
                                examResultEntity.setUploadTime(Date.from(examFileEntity.getUploadTime().atZone(ZoneId.systemDefault()).toInstant()));
                                insert(examResultEntity);
                                ExamInfo examInfo = examInfoService.selectByExamUid(examFileEntity.getExamUid());
                                insertRef(examInfo.getExamSerialNo(), examResultEntity.getReportId());
                            }
                        }

                    }
                }
            }).whenComplete((result, exception) -> {
                taskEntity.setOcrDiagTaskStatus(OCRTaskStatus.SUCCESS);
                if (exception != null) {
                    log.error(exception.getMessage(), exception);
                    taskEntity.setOcrDiagTaskStatus(OCRTaskStatus.FAILED);
                }
                examFileService.updateByPrimaryKeySelective(taskEntity);
            });
        }
    }

    @DataSource(value = DataSourceType.MASTER)
    private String getSysConfig(String key) {
        return configService.selectConfigByKey(key);
    }
}
