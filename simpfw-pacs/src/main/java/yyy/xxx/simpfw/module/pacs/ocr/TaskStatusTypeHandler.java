package yyy.xxx.simpfw.module.pacs.ocr;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import yyy.xxx.simpfw.module.pacs.constants.OCRTaskStatus;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class TaskStatusTypeHandler extends BaseTypeHandler<OCRTaskStatus> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, OCRTaskStatus parameter, JdbcType jdbcType) throws SQLException {
        ps.setInt(i, parameter.getCode());
    }

    @Override
    public OCRTaskStatus getNullableResult(ResultSet rs, String columnName) throws SQLException {
        int code = rs.getInt(columnName);
        if (rs.wasNull()) {
            return null;
        }
        return OCRTaskStatus.fromCode(code);
    }

    @Override
    public OCRTaskStatus getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        int code = rs.getInt(columnIndex);
        if (rs.wasNull()) {
            return null;
        }
        return OCRTaskStatus.fromCode(code);
    }

    @Override
    public OCRTaskStatus getNullableResult(java.sql.CallableStatement cs, int columnIndex) throws SQLException {
        int code = cs.getInt(columnIndex);
        if (cs.wasNull()) {
            return null;
        }
        return OCRTaskStatus.fromCode(code);
    }
}
